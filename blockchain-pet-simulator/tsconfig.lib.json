{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "jsx": "react-jsx", "strict": true, "declaration": true, "declarationDir": "./dist", "emitDeclarationOnly": true, "types": ["vite/client"], "resolveJsonModule": true, "experimentalDecorators": true, "useDefineForClassFields": false, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/index.ts", "src/components/PhaserGame.tsx"], "exclude": ["src/main.tsx", "src/App.tsx", "**/*.test.*", "**/*.spec.*"]}