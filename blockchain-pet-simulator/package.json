{"name": "pet-rising-game", "version": "1.4.0", "type": "module", "description": "A simple React component with a walking animal animation using Phaser.js", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "keywords": ["react", "phaser", "game", "animation", "dog", "component"], "author": "hyunn", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/starci-lab/blockchain-pet-simulator"}, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:lib": "vite build --mode library && npm run build:types", "build:types": "tsc -p tsconfig.lib.json", "lint": "eslint .", "preview": "vite preview", "prepublishOnly": "npm run build:lib"}, "dependencies": {"@colyseus/schema": "^3.0.42", "axios": "^1.10.0", "colyseus.js": "^0.16.19", "ethers": "^6.14.4", "path": "^0.12.7", "phaser": "^3.90.0", "phaser3-rex-plugins": "^1.80.16", "react": "^19.1.0", "react-dom": "^19.1.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.29", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}